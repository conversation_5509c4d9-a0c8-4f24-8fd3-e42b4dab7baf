<?php
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require 'vendor/autoload.php'; // Include the PHPMailer autoload file

$response = [
    'success' => false,
    'message' => ''
];

try {
    // Validate the form inputs
    if (empty($_POST['name']) || empty($_POST['email1']) || empty($_POST['message'])) {
        throw new Exception('Please fill in all required fields.');
    }

    $name = htmlspecialchars(strip_tags(trim($_POST['name'])));
    $email = htmlspecialchars(strip_tags(trim($_POST['email1'])));
    $message = htmlspecialchars(strip_tags(trim($_POST['message'])));

    // Initialize PHPMailer
    $mail = new PHPMailer(true);

    // Set up the mailer
    $mail->isSMTP();
    $mail->Host = 'smtp.gmail.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';  
    $mail->Password = 'wdjhnwoaighmrfhn'; 
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;

    // Recipients
    $mail->setFrom('<EMAIL>', 'Kulfi Printex');
    $mail->addAddress('contact@Kulfi Printex.com');

    $mail->isHTML(true);
    $mail->Subject = 'New Contact Form Submission';
    $mail->Body = "Name: $name<br>Email: $email<br>Message: $message";
    $mail->AltBody = "Name: $name\nEmail: $email\nMessage: $message";

   
    if ($mail->send()) {
        $response['success'] = true;
        $response['message'] = 'Message sent successfully!';
    } else {
        throw new Exception('Message could not be sent. Mailer Error: ' . $mail->ErrorInfo);
    }
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

header('Content-Type: application/json');
echo json_encode($response);
?>
