/*!
 * Innfinitix Custom Styles
 * Custom CSS for Innfinitix Printing Solutions Website
 * Includes responsive design, hero section, product badges, and service buttons
 * Author: Innfinitix Development Team
 * Version: 1.0
 */

/* ==========================================================================
   ENHANCED LANDING PAGE STYLES
   ========================================================================== */

/* Enhanced hover effects for product cards */
.product .grid-inner:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced service cards hover effects */
.feature-box:hover {
    transform: translateY(-8px);
    transition: all 0.3s ease;
}

/* Smooth animations for buttons */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Enhanced image hover effects */
.hover-lift:hover {
    transform: translateY(-5px) scale(1.02);
}

/* Gradient text animations */
.section-title-color {
    background: linear-gradient(135deg, #4075DC 20%, #EC008C  80%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite alternate;
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }

    100% {
        background-position: 100% 50%;
    }
}

/* Enhanced card shadows */
.shadow-sm {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
}

.shadow-lg {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

/* ==========================================================================
   RESPONSIVE DESIGN - MOBILE FIRST APPROACH
   ========================================================================== */

/* Mobile First Approach (320px-575px) */
@media (max-width: 575px) {
    .display-4 {
        font-size: 1.75rem !important;
        line-height: 1.3 !important;
    }

    .display-5 {
        font-size: 1.5rem !important;
        line-height: 1.3 !important;
    }

    .lead {
        font-size: 0.95rem !important;
        line-height: 1.5 !important;
    }

    .container {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    /* Product cards mobile optimization */
    .product-card {
        margin-bottom: 1.5rem !important;
    }

    .product-card .product-content {
        padding: 1rem !important;
    }

    .product-card .product-title {
        font-size: 0.95rem !important;
        margin-bottom: 0.75rem !important;
    }

    .product-card .price h3 {
        font-size: 1.25rem !important;
    }

    /* Service cards mobile */
    .feature-box {
        margin-bottom: 1.5rem !important;
    }

    .fbox-content {
        padding: 1rem !important;
    }

    /* Company stats mobile */
    .company-stats .col-4 {
        margin-bottom: 0.75rem;
    }

    .company-stats .col-4>div {
        padding: 0.75rem !important;
    }

    .company-stats h5 {
        font-size: 1rem !important;
    }
}

/* Tablet Responsive (576px-991px) */
@media (min-width: 576px) and (max-width: 991px) {
    .display-4 {
        font-size: 2.25rem !important;
        line-height: 1.2 !important;
    }

    .display-5 {
        font-size: 1.875rem !important;
        line-height: 1.2 !important;
    }

    .lead {
        font-size: 1.05rem !important;
        line-height: 1.6 !important;
    }

    .product-card .product-content {
        padding: 1.25rem !important;
    }

    .product-card .product-title {
        font-size: 1rem !important;
    }

    .product-card .price h3 {
        font-size: 1.375rem !important;
    }
}

/* Desktop Responsive (992px+) */
@media (min-width: 992px) {
    .display-4 {
        font-size: 2.5rem !important;
    }

    .display-5 {
        font-size: 2rem !important;
    }
}

/* Image Responsive */
@media (max-width: 767px) {

    .product-image img,
    .company-image img,
    .service-image img {
        max-height: 200px !important;
        object-fit: contain !important;
    }

    .hover-lift {
        transform: none !important;
    }

    .hover-lift:hover {
        transform: none !important;
    }
}

/* ==========================================================================
   TESTIMONIALS STYLING
   ========================================================================== */

/* Enhanced testimonial cards */
.testimonials-carousel .card:hover {
    transform: translateY(-5px);
    transition: all 0.3s ease;
}

/* Loading animation for images */
.img-fluid {
    transition: opacity 0.3s ease;
}

/* Enhanced form styling */
.form-control:focus {
    border-color: #5e8cf0;
    box-shadow: 0 0 0 0.2rem rgba(94, 140, 240, 0.25);
}

/* ==========================================================================
   PRODUCT CARD STYLING
   ========================================================================== */

/* New Product Card Styling */
.product-card {
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0 !important;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12) !important;
    border-color: #e0e0e0 !important;
}

.product-card .product-image {
    background: #f8f9fa !important;
    transition: all 0.3s ease;
}

.product-card:hover .product-image {
    background: #f0f2f5 !important;
}

.product-card .product-title a {
    transition: color 0.3s ease;
}

.product-card:hover .product-title a {
    color: #5e8cf0 !important;
}

.product-card .badge {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.product-card .stars i {
    font-size: 0.9rem;
}

.product-card .price h3 {
    color: #5e8cf0 !important;
}

/* Product image hover effect */
.product-card .product-image img {
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

/* ==========================================================================
   RESPONSIVE LOGO STYLING
   ========================================================================== */

.responsive-logo {
    transition: all 0.3s ease;
    max-height: 60px;
}

/* Header responsive adjustments */
@media (max-width: 575px) {
    #header .header-row {
        padding: 0.75rem 0 !important;
    }

    .responsive-logo {
        height: clamp(30px, 10vw, 45px) !important;
        max-width: 150px !important;
    }

    #logo {
        flex: 0 0 auto !important;
        width: auto !important;
    }
}

@media (min-width: 576px) and (max-width: 991px) {
    .responsive-logo {
        height: clamp(40px, 8vw, 55px) !important;
        max-width: 200px !important;
    }
}

@media (min-width: 992px) {
    .responsive-logo {
        height: clamp(45px, 6vw, 60px) !important;
        max-width: 250px !important;
    }
}

/* Ensure logo container doesn't overflow */
#logo a {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
}

/* Mobile menu trigger positioning */
@media (max-width: 991px) {
    .primary-menu-trigger {
        margin-left: auto;
        display: flex !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        z-index: 10 !important;
    }

    #header .header-row {
        justify-content: space-between !important;
    }

    /* Ensure mobile menu trigger is visible */
    .primary-menu-trigger .cnvs-hamburger {
        background: transparent !important;
        border: none !important;
        padding: 8px !important;
        cursor: pointer !important;
    }

    /* Style hamburger lines with Innfinitix colors */
    .cnvs-hamburger .cnvs-hamburger-inner,
    .cnvs-hamburger .cnvs-hamburger-inner::before,
    .cnvs-hamburger .cnvs-hamburger-inner::after {
        background-color: #1659e9 !important;
        transition: all 0.3s ease !important;
    }

    /* Active state for hamburger */
    .primary-menu-trigger-active .cnvs-hamburger .cnvs-hamburger-inner {
        background-color: #EC008C !important;
    }

    .primary-menu-trigger-active .cnvs-hamburger .cnvs-hamburger-inner::before,
    .primary-menu-trigger-active .cnvs-hamburger .cnvs-hamburger-inner::after {
        background-color: #EC008C !important;
    }
}

/* ==========================================================================
   MOBILE NAVIGATION RESPONSIVE FIXES
   ========================================================================== */

/* Mobile Navigation Menu Styles (320px-991px) */
@media (max-width: 991px) {

    /* Primary Menu Container */
    .primary-menu {
        width: 100% !important;
        flex-basis: 100% !important;
    }

    /* Menu Container Styling */
    .menu-container {
        display: none !important;
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        background: #5e8cf0 !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 0 0 8px 8px !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        z-index: 999 !important;
        max-height: 70vh !important;
        overflow-y: auto !important;
        padding: 1rem 0 !important;
    }

    /* Show menu when active */
    .menu-container.d-block {
        display: block !important;
    }

    /* Menu Items */
    .menu-container .menu-item {
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin: 0 !important;
    }

    .menu-container .menu-item:first-child {
        border-top: none !important;
    }

    /* Menu Links */
    .menu-container .menu-item .menu-link {
        display: block !important;
        padding: 12px 20px !important;
        color: #ffffff !important;
        font-weight: 600 !important;
        font-size: 1rem !important;
        text-decoration: none !important;
        transition: all 0.3s ease !important;
        border-left: 3px solid transparent !important;
    }

    /* Menu Link Hover Effects */
    .menu-container .menu-item .menu-link:hover {
        background: #1659e9 !important;
        border-left-color: #EC008C !important;
        color: #ffffff !important;
        padding-left: 25px !important;
    }

    /* Active Menu Item */
    .menu-container .menu-item.current .menu-link {
        background: #1659e9 !important;
        border-left-color: #EC008C !important;
        color: #ffffff !important;
    }

    /* Sub-menu styling */
    .sub-menu-container {
        position: relative !important;
        background: rgba(22, 89, 233, 0.9) !important;
        border: none !important;
        box-shadow: none !important;
        padding-left: 20px !important;
    }

    .sub-menu-container .menu-item .menu-link {
        padding: 10px 20px !important;
        font-size: 0.9rem !important;
        color: rgba(255, 255, 255, 0.9) !important;
    }

    .sub-menu-container .menu-item .menu-link:hover {
        background: rgba(236, 0, 140, 0.2) !important;
        color: #ffffff !important;
    }

    /* Sub-menu trigger button */
    .menu-item .sub-menu-trigger {
        background: transparent !important;
        color: #ffffff !important;
        border: none !important;
        font-size: 14px !important;
        padding: 8px !important;
        right: 10px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
    }

    .menu-item .sub-menu-trigger:hover {
        color: #EC008C !important;
    }
}

/* Tablet Specific Adjustments (768px-991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .menu-container {
        max-height: 60vh !important;
    }

    .menu-container .menu-item .menu-link {
        font-size: 1.1rem !important;
        padding: 14px 25px !important;
    }
}

/* Mobile Specific Adjustments (320px-767px) */
@media (max-width: 767px) {
    .menu-container {
        max-height: 50vh !important;
        border-radius: 0 !important;
        left: -15px !important;
        right: -15px !important;
        width: calc(100% + 30px) !important;
    }

    .menu-container .menu-item .menu-link {
        font-size: 0.95rem !important;
        padding: 10px 15px !important;
    }

    .menu-container .menu-item .menu-link:hover {
        padding-left: 20px !important;
    }
}

/* Ensure menu works with body classes */
body.primary-menu-open {
    overflow-x: hidden !important;
}

/* Fix for menu positioning */
@media (max-width: 991px) {
    #header {
        position: relative !important;
    }

    .primary-menu {
        position: relative !important;
    }

    /* Additional mobile menu fixes */
    .primary-menu-active .menu-container {
        display: block !important;
    }

    /* Ensure hamburger is clickable */
    .primary-menu-trigger {
        cursor: pointer !important;
        user-select: none !important;
        -webkit-tap-highlight-color: transparent !important;
    }

    /* Fix for menu container visibility */
    .menu-container:not(.d-block) {
        display: none !important;
    }

    /* Smooth transitions */
    .menu-container {
        transition: all 0.3s ease !important;
        transform-origin: top center !important;
    }

    .menu-container.d-block {
        animation: slideDown 0.3s ease forwards !important;
    }

    /* Animation for menu appearance */
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Ensure menu items are clickable */
    .menu-container .menu-item .menu-link {
        pointer-events: auto !important;
        cursor: pointer !important;
    }

    /* Fix z-index issues */
    .primary-menu {
        z-index: 998 !important;
    }

    .menu-container {
        z-index: 999 !important;
    }

    /* Accessibility improvements */
    .primary-menu-trigger:focus {
        outline: 2px solid #EC008C !important;
        outline-offset: 2px !important;
    }

    .menu-container .menu-item .menu-link:focus {
        outline: 2px solid #EC008C !important;
        outline-offset: -2px !important;
        background: #1659e9 !important;
    }
}

/* ==========================================================================
   HERO SECTION RESPONSIVE DESIGN FIXES
   ========================================================================== */

/* Mobile and Tablet Responsive Alignment (320px-991px) */
@media (max-width: 991px) {

    /* Main Heading Center Alignment */
    #hero-section .hero-content h1 {
        text-align: center !important;
    }

    /* Description Text Center Alignment */
    #hero-section .hero-content .lead {
        text-align: center !important;
    }

    /* Features List Center Alignment */
    #hero-section .hero-content .row.text-dark {
        text-align: center !important;
    }

    #hero-section .hero-content .iconlist {
        padding-left: 15px !important;
    }

    /* Call-to-Action Buttons Center Alignment */
    #hero-section .company-cta {
        text-align: center !important;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    /* Button Spacing and Stacking */
    #hero-section .company-cta .btn {
        margin-right: 0 !important;
        margin-bottom: 0.75rem;
        min-width: 200px;
        padding: 0.75rem 1.5rem;
    }

    #hero-section .company-cta .btn:last-child {
        margin-bottom: 0;
    }
}

/* Mobile Specific Adjustments (320px-767px) */
@media (max-width: 767px) {

    /* Hero Badge Center Alignment */
    #hero-section .hero-badge {
        text-align: center !important;
    }

    /* Reduce heading size on mobile */
    #hero-section .hero-content h1 {
        font-size: clamp(1.75rem, 8vw, 2.5rem) !important;
        line-height: 1.2;
    }

    /* Features List Mobile Layout */
    #hero-section .hero-content .row.text-dark .col-sm-6 {
        margin-bottom: 1rem;
    }

    /* Button Full Width on Small Mobile */
    #hero-section .company-cta .btn {
        width: 100%;
        max-width: 280px;
    }

    /* Hero Content Padding */
    #hero-section .hero-content {
        padding: 1rem 0;
    }
}

/* Tablet Specific Adjustments (768px-991px) */
@media (min-width: 768px) and (max-width: 991px) {

    /* Features List Tablet Layout */
    #hero-section .hero-content .row.text-dark {
        justify-content: center;
    }

    /* Button Layout for Tablet */
    #hero-section .company-cta {
        flex-direction: row;
        justify-content: center;
        gap: 1.5rem;
    }

    #hero-section .company-cta .btn {
        margin-bottom: 0;
        min-width: 180px;
    }
}

/* Desktop Layout Preservation (992px+) */
@media (min-width: 992px) {

    /* Keep original desktop alignment */
    #hero-section .hero-content h1,
    #hero-section .hero-content .lead,
    #hero-section .hero-content .row.text-dark,
    #hero-section .company-cta {
        text-align: left !important;
    }

    #hero-section .company-cta {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        gap: 1rem;
    }

    #hero-section .company-cta .btn {
        margin-right: 1rem;
        margin-bottom: 0;
        min-width: auto;
    }
}

/* Additional Brand Color Enhancements */
#hero-section .company-cta .btn {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(22, 89, 233, 0.2);
}

#hero-section .company-cta .btn:hover {
    background: #5e8cf0 !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(22, 89, 233, 0.3);
}

#hero-section .company-cta .btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(22, 89, 233, 0.25);
}

/* ==========================================================================
   SERVICE CTA BUTTON RESPONSIVE DESIGN
   ========================================================================== */

.service-cta-btn {
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(22, 89, 233, 0.2);
}

.service-cta-btn:hover {
    background: #5e8cf0 !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(22, 89, 233, 0.3);
}

.service-cta-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(22, 89, 233, 0.25);
}

/* Mobile and Tablet Responsive (320px-991px) */
@media (max-width: 991px) {
    .service-cta {
        text-align: center !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .service-cta-btn {
        width: 45% !important;
        min-width: 180px;
        max-width: 250px;
        display: inline-block;
        text-align: center;
        margin: 0 auto !important;
    }
}

/* Mobile Specific (320px-767px) */
@media (max-width: 767px) {
    .service-cta-btn {
        width: 45% !important;
        min-width: 160px;
        max-width: 200px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

/* Tablet Specific (768px-991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .service-cta-btn {
        width: 45% !important;
        min-width: 200px;
        max-width: 280px;
        padding: 0.75rem 1.5rem;
    }
}

/* Desktop (992px+) - Keep original styling */
@media (min-width: 992px) {
    .service-cta {
        text-align: left !important;
    }

    .service-cta-btn {
        width: auto !important;
        min-width: auto;
        max-width: none;
    }
}

/* ==========================================================================
   PRODUCT BADGE STANDARDIZATION
   ========================================================================== */

.product-image .badge {
    /* Consistent sizing and positioning */
    position: absolute !important;
    top: 0.75rem !important;
    right: 0.75rem !important;

    /* Standardized padding and sizing */
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;

    /* Consistent width and height */
    min-width: 70px !important;
    min-height: 28px !important;

    /* Consistent styling */
    border-radius: 50px !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    /* Enhanced shadow for better visibility */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;

    /* Smooth transitions */
    transition: all 0.3s ease !important;
}

/* Hover effects for product badges */
.product-card:hover .badge {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Mobile Responsive (320px-767px) */
@media (max-width: 767px) {
    .product-image .badge {
        top: 0.5rem !important;
        right: 0.5rem !important;
        padding: 0.4rem 0.6rem !important;
        font-size: 0.7rem !important;
        min-width: 60px !important;
        min-height: 24px !important;
    }
}

/* Tablet Responsive (768px-991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .product-image .badge {
        top: 0.6rem !important;
        right: 0.6rem !important;
        padding: 0.45rem 0.7rem !important;
        font-size: 0.72rem !important;
        min-width: 65px !important;
        min-height: 26px !important;
    }
}

/* Desktop (992px+) */
@media (min-width: 992px) {
    .product-image .badge {
        top: 0.75rem !important;
        right: 0.75rem !important;
        padding: 0.5rem 0.75rem !important;
        font-size: 0.75rem !important;
        min-width: 70px !important;
        min-height: 28px !important;
    }
}

/* Remove any existing responsive styling conflicts */
.product-image .badge[style*="font-size"],
.product-image .badge[style*="padding"],
.product-image .badge[style*="margin"] {
    font-size: 0.75rem !important;
    padding: 0.5rem 0.75rem !important;
    margin: 0 !important;
}

/* Ensure consistent positioning for all badge containers */
.product-image .position-absolute {
    top: 0.75rem !important;
    right: 0.75rem !important;
    margin: 0 !important;
}

@media (max-width: 767px) {
    .product-image .position-absolute {
        top: 0.5rem !important;
        right: 0.5rem !important;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .product-image .position-absolute {
        top: 0.6rem !important;
        right: 0.6rem !important;
    }
}